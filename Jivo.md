# Jivo Chat Integration Summary

## Main Integration (in backend/views/layouts/main.php)

The Jivo chat widget is loaded in the main layout file with the following configuration:

- **Widget ID**: `dwzeNfFJka`
- **Script Source**: `//code-eu1.jivosite.com/widget/dwzeNfFJka`
- **Loading Method**: Asynchronous loading (j.async = true)
- **Loading Delay**: 5-second delay using setTimeout
- **Insertion Point**: Appended to document.body
- **Conditional Loading**: Only loads if the cookie 'dJ' is not set
- **Loading Order**: Loaded after Google Tag Manager

```javascript
setTimeout(function (){
    let j = document.createElement('script');
    j.async = true;
    j.src = '//code-eu1.jivosite.com/widget/dwzeNfFJka';
    document.body.appendChild(j);
}, 5000)
```

## Callback Button Integration (in success.php files)

The success pages include a custom "Call Back" button that interacts with Ji<PERSON>'s callback functionality:

- **Button ID**: `jivo_callback_btn`
- **Interaction Method**: Programmatically triggers Ji<PERSON>'s built-in callback button
- **Integration Hook**: Uses `jivo_onLoadCallback()` function

```javascript
function openJivoCallButton() {
    const jivoCallbackBtn = document.querySelector('jdiv.__jivoCallbackBtn')
    if(jivoCallbackBtn){
        jivoCallbackBtn.click()
    }
}

function jivo_onLoadCallback() {
    document.getElementById('jivo_callback_btn').addEventListener("click", openJivoCallButton);
}
```

## Custom Styling (in frontend/static/app/styles/app-base.css)

The Jivo chat widget is styled to match the website's design:

- **Font Family**: sbc-font, sans-serif
- **Input Border Color**: #D7D7D7
- **Button Styling**: Gradient from #9d1d5a to #590c32
- **Brand Colors**: Uses the site's brand colors for buttons

## Disabling Mechanism

Jivo chat can be disabled by setting a cookie named 'dJ'.
