export default defineNuxtPlugin(() => {
    // Только на клиенте
    if (import.meta.server) return

    const config = useRuntimeConfig()
    const jivoConfig = config.public.jivo

    // Проверяем, включен ли Jivo в конфигурации
    if (!jivoConfig.enabled) {
        console.log('Jivo chat disabled in configuration')
        return
    }

    // Проверяем, не отключен ли чат через cookie
    const isJivoDisabled = () => {
        if (typeof document === 'undefined') return true
        return document.cookie.includes('dJ=')
    }

    // Функция для загрузки Jivo скрипта
    const loadJivoScript = () => {
        if (isJivoDisabled()) {
            console.log('Jivo chat disabled by cookie')
            return
        }

        // Проверяем, не загружен ли уже скрипт
        if (document.querySelector('script[src*="jivosite.com"]')) {
            console.log('Jivo script already loaded')
            return
        }

        const script = document.createElement('script')
        script.async = true
        script.src = `//code-eu1.jivosite.com/widget/${jivoConfig.widgetId}`

        script.onload = () => {
            console.log('Jivo chat loaded successfully')
        }

        script.onerror = () => {
            console.error('Failed to load Jivo chat')
        }

        document.body.appendChild(script)
    }

    // Загружаем скрипт с задержкой (как в старой версии)
    setTimeout(() => {
        loadJivoScript()
    }, jivoConfig.loadDelay || 5000)

    // Предоставляем глобальные функции для работы с Jivo
    return {
        provide: {
            jivo: {
                // Функция для программного открытия callback кнопки
                openCallbackButton: () => {
                    if (import.meta.server) return

                    const jivoCallbackBtn = document.querySelector('jdiv.__jivoCallbackBtn') as HTMLElement
                    if (jivoCallbackBtn) {
                        jivoCallbackBtn.click()
                    } else {
                        console.warn('Jivo callback button not found')
                    }
                },

                // Функция для отключения чата (установка cookie)
                disable: () => {
                    if (import.meta.server) return

                    document.cookie = 'dJ=1; path=/; max-age=31536000' // 1 год
                    console.log('Jivo chat disabled')
                },

                // Функция для включения чата (удаление cookie)
                enable: () => {
                    if (import.meta.server) return

                    document.cookie = 'dJ=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
                    console.log('Jivo chat enabled')
                    loadJivoScript()
                },

                // Проверка статуса
                isDisabled: isJivoDisabled
            }
        }
    }
})

// Глобальная функция для callback (как в старой версии)
declare global {
    interface Window {
        jivo_onLoadCallback?: () => void
    }
}
