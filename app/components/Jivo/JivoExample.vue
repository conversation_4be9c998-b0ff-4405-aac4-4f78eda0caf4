<template>
    <div class="jivo-example">
        <h3>Jivo Chat Integration Example</h3>
        
        <div class="status-info">
            <p><strong>Chat Status:</strong> {{ isDisabled ? 'Disabled' : 'Enabled' }}</p>
            <p><strong>Script Loaded:</strong> {{ isLoaded ? 'Yes' : 'No' }}</p>
        </div>

        <div class="controls">
            <JivoCallbackButton class="custom-callback-btn">
                📞 Request Callback
            </JivoCallbackButton>

            <button 
                v-if="!isDisabled" 
                @click="disable"
                class="control-btn disable-btn"
            >
                Disable Chat
            </button>

            <button 
                v-if="isDisabled" 
                @click="enable"
                class="control-btn enable-btn"
            >
                Enable Chat
            </button>
        </div>

        <div class="info">
            <p><small>
                This example shows how to integrate and control Jivo chat.
                The chat will load automatically after 5 seconds (configurable).
            </small></p>
        </div>
    </div>
</template>

<script setup lang="ts">
const { disable, enable, isDisabled, isLoaded } = useJivo()
</script>

<style scoped>
.jivo-example {
    max-width: 500px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
}

.status-info {
    margin-bottom: 20px;
    padding: 10px;
    background: white;
    border-radius: 4px;
}

.controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.control-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.disable-btn {
    background: #dc3545;
    color: white;
}

.enable-btn {
    background: #28a745;
    color: white;
}

.custom-callback-btn {
    background: linear-gradient(135deg, #9d1d5a 0%, #590c32 100%);
}

.info {
    padding: 10px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 14px;
}
</style>
