<template>
    <div
        ref="placeRef"
        class="itinerary-place"
        :class="{ 'itinerary-place--open': isOpen, 'itinerary-place--open-options': isOpen && isOptions }"
        @click="openSearch"
    >
        <div
            ref="placeSearchRef"
            class="itinerary-place__wrapper"
            :class="{
                'itinerary-place__wrapper--open': isOpen
            }"
        >
            <div class="itinerary-place__field" :class="{ 'itinerary-place__field--open': isOpen }">
                <div
                    class="itinerary-place__field__wrapper"
                    :class="{ 'itinerary-place__field__wrapper--open': isOpen && isOptions}"
                >
                    <div class="itinerary-place__field__icon">
                        <IconSearch v-if="isOpen" />
                        <svg
                            v-else-if="isCity"
                            xmlns="http://www.w3.org/2000/svg"
                            width="16pt"
                            height="16pt"
                            viewBox="0 0 16 16"
                        >
                            <path d="M13.191 13.516c-.007-.973-.02-5.606.434-7.352l-1.727-1.418v8.438a6.992 6.992 0 00-.304-.063V4.742l-1.98 1.32c.257 1.149.374 5.153.417 6.86-.113-.012-.226-.02-.34-.027 0-.497.008-.993.028-1.493-.024-.707-.055-1.5-.09-2.261l-.336-.106v-.781h.285c-.058-.922-.133-1.715-.226-2.121l-.047-.215.465-.309-.36-.117.13-.691.483.152-.062.527.246-.164c.207-1.757.398-2.933.398-2.933L8.242.503v12.333h-.226V.328L5.375 2.36s.094.602.219 1.59l.601.45v-.563l.547-.207v.656l-.402.219.594.441-.055.211c-.29 1.074-.52 3.399-.672 5.356.047.789.074 1.59.09 2.383-.152.011-.3.023-.445.039.105-1.79.402-6.215.843-7.86l-2.082-1.55v9.581c-.101.02-.207.036-.304.06V3.526l-2.055 1.52c.527 1.808.703 7.383.73 8.469C.461 14.367 0 15.71 0 15.71h16.04s-.274-1.344-2.849-2.195zM9.676 2.414l.594.281-.082.727-.579-.215zM9.609 3.54l.547.223-.117.82-.5-.2zm-3.414-.117l-.164-.695.563-.293.148.773zm-.347 1.656l.52.277-.11.52-.446-.133zm-.075.945l.43.11-.047.57-.383-.148zm-.05.832l.382.157-.043.644-.378-.183zm-.078.934l.335.063-.043.57-.292-.098zm-.07.867h.362v.64l-.363-.026zm-.173 3.23l-.398-.097v-.664l.398.164zm0-.913l-.398-.098v-.664l.398.168zm0-.914l-.398-.098v-.664l.398.168zm0-.91l-.398-.102v-.66l.398.164zm0-.915l-.398-.101v-.66l.398.164zm0-.914l-.398-.101v-.66l.398.164zm0-.914l-.398-.097v-.664l.398.168zm0-.914l-.398-.097V4.73l.398.168zm.371 6.395h-.261v-.57h.261zm0-.914h-.261v-.57h.261zm0-.914h-.261v-.57h.261zm.82-2.825l.337-.191.062.812-.398.06zm.485 4.082h-.285v-.757h.285zm0-1.129l-.336.047v-.832l.336-.062zm-.336-1.199v-.691l.25-.074.086.742zm.946 2.223l-.333.156v-.844l.332-.191zm0-1.211l-.333.188V9.34l.332-.219zm0-1.21l-.333.175V8.12l.332-.207zm0-1.208l-.407.18v-.844l.407-.215zm0-1.21l-.45.183V5.71l.45-.215zm0-1.208l-.45.156v-.843l.45-.192zm0-1.21l-.497.23V3.34l.497-.266zm0-1.212l-.563.215v-.844l.563-.246zm.93-.875l.585.328V3l-.586-.305zm.288 9.5l-.289-.097v-.81l.29.099zm0-1.18l-.289-.105V9.055l.29.18zm.086-1.222l-.375-.266v-.844l.375.282zm0-1.203l-.375-.278v-.828l.375.305zM9.047 6.5l-.43-.21v-.83l.43.24zm.039-1.145l-.469-.265v-.828l.469.293zm0-1.25l-.469-.214v-.829l.469.243zm.398 7.262L9.2 11.27v-.81l.285.099zm.055-1.133l-.305-.046v-.81l.305.067zm3.266-4.078l.437.211v.488l-.437-.246zm-.086 3.067l.293.105v.488l-.293-.144zm0 .793l.293.105v.488l-.293-.144zm0 .793l.293.105v.488l-.293-.144zm-.25 1.625l-.348-.137v-.555l.348.172zm0-.743l-.348-.136v-.551l.348.168zm0-.742l-.348-.136v-.551l.348.168zm0-.738l-.348-.14V9.52l.348.168zm0-.742l-.348-.137v-.555l.348.168zm0-.742l-.348-.137v-.555l.348.168zm0-.743l-.348-.136v-.551l.348.168zm0-.742l-.348-.137v-.55l.348.168zm0-.742l-.348-.133v-.554l.348.167zm.586 5.645l-.336-.094v-.45l.336.051zm0-3.075l-.336-.191v-.45l.336.153zm.097-.734l-.433-.191v-.508l.433.207zm0-.742l-.433-.25v-.45l.433.208zm0 0" />
                        </svg>
                        <svg
                            v-else
                            xmlns="http://www.w3.org/2000/svg"
                            width="16pt"
                            height="16pt"
                            viewBox="0 0 16 16"
                        >
                            <path
                                d="M15.71.29c.335.397.38.96.137 1.69a4.982 4.982 0 01-1.233 1.964l-1.838 1.838 1.827 7.946c.038.145-.008.27-.137.377L13.005 15.2a.322.322 0 01-.217.068.253.253 0 01-.08-.011.316.316 0 01-.24-.183l-3.185-5.8-2.957 2.957.605 2.215a.348.348 0 01-.091.354l-1.096 1.096A.356.356 0 015.48 16h-.023a.41.41 0 01-.274-.148l-2.158-2.877L.15 10.817c-.083-.053-.133-.14-.148-.263a.391.391 0 01.103-.285L1.2 9.16a.356.356 0 01.262-.102c.046 0 .076.004.092.011l2.215.605 2.956-2.957-5.8-3.185a.382.382 0 01-.193-.274.352.352 0 01.102-.308L2.296 1.49c.106-.1.22-.13.342-.092l7.592 1.815 1.827-1.826A4.983 4.983 0 0114.02.154c.73-.244 1.293-.198 1.69.137z"
                            />
                        </svg>
                    </div>
                    <div class="itinerary-place__input__container">
                        <label class="itinerary-place__input__label">{{ label }}</label>
                        <input
                            ref="inputRef"
                            v-model="selectedOption"
                            type="text"
                            class="itinerary-place__input"
                            :placeholder="placeholder"
                            @input="handleInputChange"
                            @keydown.esc="closeSearch"
                        >
                    </div>
                </div>
                <div v-if="isOptions" class="itinerary-place__field__options">
                    <div
                        v-for="(place, index) in fetchedPlaces"
                        :key="index"
                        class="itinerary-place__field__options__item group"
                        @click.stop="selectPlace(place)"
                    >
                        <div v-if="place.type === 'city'" class="flex w-full h-full">
                            <div class="itinerary-place__field__options__item__icon__container">
                                <div class="itinerary-place__field__options__item__icon">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16pt"
                                        height="16pt"
                                        viewBox="0 0 16 16"
                                    >
                                        <path d="M13.191 13.516c-.007-.973-.02-5.606.434-7.352l-1.727-1.418v8.438a6.992 6.992 0 00-.304-.063V4.742l-1.98 1.32c.257 1.149.374 5.153.417 6.86-.113-.012-.226-.02-.34-.027 0-.497.008-.993.028-1.493-.024-.707-.055-1.5-.09-2.261l-.336-.106v-.781h.285c-.058-.922-.133-1.715-.226-2.121l-.047-.215.465-.309-.36-.117.13-.691.483.152-.062.527.246-.164c.207-1.757.398-2.933.398-2.933L8.242.503v12.333h-.226V.328L5.375 2.36s.094.602.219 1.59l.601.45v-.563l.547-.207v.656l-.402.219.594.441-.055.211c-.29 1.074-.52 3.399-.672 5.356.047.789.074 1.59.09 2.383-.152.011-.3.023-.445.039.105-1.79.402-6.215.843-7.86l-2.082-1.55v9.581c-.101.02-.207.036-.304.06V3.526l-2.055 1.52c.527 1.808.703 7.383.73 8.469C.461 14.367 0 15.71 0 15.71h16.04s-.274-1.344-2.849-2.195zM9.676 2.414l.594.281-.082.727-.579-.215zM9.609 3.54l.547.223-.117.82-.5-.2zm-3.414-.117l-.164-.695.563-.293.148.773zm-.347 1.656l.52.277-.11.52-.446-.133zm-.075.945l.43.11-.047.57-.383-.148zm-.05.832l.382.157-.043.644-.378-.183zm-.078.934l.335.063-.043.57-.292-.098zm-.07.867h.362v.64l-.363-.026zm-.173 3.23l-.398-.097v-.664l.398.164zm0-.913l-.398-.098v-.664l.398.168zm0-.914l-.398-.098v-.664l.398.168zm0-.91l-.398-.102v-.66l.398.164zm0-.915l-.398-.101v-.66l.398.164zm0-.914l-.398-.101v-.66l.398.164zm0-.914l-.398-.097v-.664l.398.168zm0-.914l-.398-.097V4.73l.398.168zm.371 6.395h-.261v-.57h.261zm0-.914h-.261v-.57h.261zm0-.914h-.261v-.57h.261zm.82-2.825l.337-.191.062.812-.398.06zm.485 4.082h-.285v-.757h.285zm0-1.129l-.336.047v-.832l.336-.062zm-.336-1.199v-.691l.25-.074.086.742zm.946 2.223l-.333.156v-.844l.332-.191zm0-1.211l-.333.188V9.34l.332-.219zm0-1.21l-.333.175V8.12l.332-.207zm0-1.208l-.407.18v-.844l.407-.215zm0-1.21l-.45.183V5.71l.45-.215zm0-1.208l-.45.156v-.843l.45-.192zm0-1.21l-.497.23V3.34l.497-.266zm0-1.212l-.563.215v-.844l.563-.246zm.93-.875l.585.328V3l-.586-.305zm.288 9.5l-.289-.097v-.81l.29.099zm0-1.18l-.289-.105V9.055l.29.18zm.086-1.222l-.375-.266v-.844l.375.282zm0-1.203l-.375-.278v-.828l.375.305zM9.047 6.5l-.43-.21v-.83l.43.24zm.039-1.145l-.469-.265v-.828l.469.293zm0-1.25l-.469-.214v-.829l.469.243zm.398 7.262L9.2 11.27v-.81l.285.099zm.055-1.133l-.305-.046v-.81l.305.067zm3.266-4.078l.437.211v.488l-.437-.246zm-.086 3.067l.293.105v.488l-.293-.144zm0 .793l.293.105v.488l-.293-.144zm0 .793l.293.105v.488l-.293-.144zm-.25 1.625l-.348-.137v-.555l.348.172zm0-.743l-.348-.136v-.551l.348.168zm0-.742l-.348-.136v-.551l.348.168zm0-.738l-.348-.14V9.52l.348.168zm0-.742l-.348-.137v-.555l.348.168zm0-.742l-.348-.137v-.555l.348.168zm0-.743l-.348-.136v-.551l.348.168zm0-.742l-.348-.137v-.55l.348.168zm0-.742l-.348-.133v-.554l.348.167zm.586 5.645l-.336-.094v-.45l.336.051zm0-3.075l-.336-.191v-.45l.336.153zm.097-.734l-.433-.191v-.508l.433.207zm0-.742l-.433-.25v-.45l.433.208zm0 0" />
                                    </svg>
                                </div>
                            </div>
                            <div class="itinerary-place__field__options__item__info">
                                <div class="itinerary-place__field__options__item__info__city">
                                    {{ place.name }}
                                </div>
                                <div class="itinerary-place__field__options__item__info__country">
                                    {{ place.country.name }}
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="itinerary-place__field__options__item__code">
                                    {{ place.code }}
                                </div>
                                <svg
                                    class="w-6 h-6 stroke-gradient group-hover:text-white group-hover:stroke-current ml-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M9 5l7 7-7 7"
                                    />
                                </svg>
                            </div>
                        </div>
                        <div v-else class="flex w-full h-full">
                            <div class="itinerary-place__field__options__item__icon__container">
                                <div class="itinerary-place__field__options__item__icon">
                                    <svg
                                        class="h-4 stroke-current text-secondary-600 group-hover:text-white -mt-1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 14 19"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M9 18l4-4.667L9 8"
                                        />
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M12.333 13.333H1V1"
                                        />
                                    </svg>
                                </div>
                            </div>
                            <div class="itinerary-place__field__options__item__icon__container">
                                <div class="itinerary-place__field__options__item__icon itinerary-place__field__options__item__icon--airport">
                                    <svg
                                        class="w-4 fill-current text-secondary-600"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 16 16"
                                    >
                                        <path d="M15.71.29c.335.397.38.96.137 1.69a4.982 4.982 0 01-1.233 1.964l-1.838 1.838 1.827 7.946c.038.145-.008.27-.137.377L13.005 15.2a.322.322 0 01-.217.068.253.253 0 01-.08-.011.316.316 0 01-.24-.183l-3.185-5.8-2.957 2.957.605 2.215a.348.348 0 01-.091.354l-1.096 1.096A.356.356 0 015.48 16h-.023a.41.41 0 01-.274-.148l-2.158-2.877L.15 10.817c-.083-.053-.133-.14-.148-.263a.391.391 0 01.103-.285L1.2 9.16a.356.356 0 01.262-.102c.046 0 .076.004.092.011l2.215.605 2.956-2.957-5.8-3.185a.382.382 0 01-.193-.274.352.352 0 01.102-.308L2.296 1.49c.106-.1.22-.13.342-.092l7.592 1.815 1.827-1.826A4.983 4.983 0 0114.02.154c.73-.244 1.293-.198 1.69.137z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="itinerary-place__field__options__item__info">
                                <div class="itinerary-place__field__options__item__info__city">
                                    {{ place.name }}
                                </div>
                                <div class="itinerary-place__field__options__item__info__country">
                                    {{ place.country.name }}
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="itinerary-place__field__options__item__code">
                                    {{ place.code }}
                                </div>
                                <svg
                                    class="w-6 h-6 stroke-gradient group-hover:text-white group-hover:stroke-current ml-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M9 5l7 7-7 7"
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onClickOutside } from '@vueuse/core'
import type { Aero } from '@tmg/aero-data-sdk'

const props = defineProps<{
    label: string,
    placeholder: string,
}>()

const aeroDataStore = useAeroDataStore()

const placeSearchRef = ref(null)

const isOpen = ref(false)

const isOptions = computed(() => {
    return fetchedPlaces.value.length > 0
})

const isCity = computed(() => {
    return lastSelectedOption.value?.type === 'city'
})

function openSearch() {
    if (isOpen.value) { return }
    isOpen.value = true
    nextTick(() => {
        inputRef.value?.focus()
    })
}

onClickOutside(placeSearchRef, () => {
    closeSearch()
})

//

const selectedOption = ref()
const lastSelectedOption = ref<Aero.PlaceSuggestion>()

const fetchedPlaces = ref([])

const inputRef = ref<HTMLInputElement | null>(null)

async function handleInputChange(event: Event) {
    const target = event.target as HTMLInputElement

    if (target.value.trim().length >= 2) {
        fetchedPlaces.value = await aeroDataStore.searchPlaces(target.value)
    }
}

function selectPlace(place: Aero.PlaceSuggestion) {
    lastSelectedOption.value = place
    closeSearch()
}

function closeSearch() {
    isOpen.value = false
    fetchedPlaces.value = []

    if (lastSelectedOption.value) {
        selectedOption.value = `${lastSelectedOption.value.name} (${lastSelectedOption.value.code})`
    }
}
</script>
