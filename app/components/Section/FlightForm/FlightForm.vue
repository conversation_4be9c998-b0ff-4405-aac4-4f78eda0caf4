<template>
    <div class="flight-form__layout">
        <div class="flight-form__layout__container">
            <div class="flight-form">
                <TripClassSelector v-model="flightFormData.tripClass" />
                <TripTypeSelector v-model="flightFormData.tripType" />
                <ItineraryManager :trip-type="flightFormData.tripType" :routes="flightFormData.routes" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import TripClassSelector from '~/components/Section/FlightForm/TripClassSelector.vue'
import TripTypeSelector from '~/components/Section/FlightForm/TripTypeSelector.vue'
import ItineraryManager from '~/components/Section/FlightForm/ItineraryManager.vue'

const { flightFormData } = useSearchFlightStore()
</script>
