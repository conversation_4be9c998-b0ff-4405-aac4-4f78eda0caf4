<template>
    <div class="itinerary-manager">
        <div class="itinerary-manager__container">
            <div
                v-for="(route, index) in routes"
                :key="index"
                class="itinerary-manager__item"
            >
                <div
                    class="itinerary-manager__item__cities"
                    :class="{
                        'max-w-[496px]': index === 0 && selectedTripType !== TripType.MultiCity
                    }"
                >
                    <ItineraryPlaceInput label="From" placeholder="Flying from?" />
                    <div class="itinerary-manager__item__cities__switch-button" :class="{'itinerary-manager__item__cities__switch-button--visible': isVisible }">
                        <svg
                            class="w-5 h-5 md:w-6 md:h-6 fill-current text-grey-400"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 23 23"
                        >
                            <path
                                d="M17.25 3.833l-3.832 3.833h2.875v6.709a1.916 1.916 0 11-3.834 0V7.666a3.838 3.838 0 00-3.833-3.833 3.838 3.838 0 00-3.834 3.833v6.709H1.917l3.834 3.833 3.833-3.833H6.71V7.666a1.916 1.916 0 113.834 0v6.709a3.838 3.838 0 003.833 3.833 3.838 3.838 0 003.833-3.833V7.666h2.875l-3.833-3.833z"
                                fill="#FFF"
                            />
                        </svg>
                    </div>
                    <ItineraryPlaceInput label="To" placeholder="Where are you flying?" />
                </div>
                <div class="min-w-[360px]">
                    test
                </div>
                <div class="min-w-[190px]">
                    tete2
                </div>
                <div class="min-w-[60px] min-h-[60px] bg-black rounded-full flex items-center justify-center">
                    <svg
                        width="19"
                        height="19"
                        viewBox="0 0 19 19"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M16.4393 18.0607C17.0251 18.6464 17.9749 18.6464 18.5607 18.0607C19.1464 17.4749 19.1464 16.5251 18.5607 15.9393L16.4393 18.0607ZM12.4393 14.0607L16.4393 18.0607L18.5607 15.9393L14.5607 11.9393L12.4393 14.0607Z"
                            fill="#F4F6F7"
                        />
                        <circle
                            cx="8"
                            cy="8"
                            r="6.5"
                            stroke="#F4F6F7"
                            strokeWidth="3"
                        />
                    </svg>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { type FlightRoute, TripType } from '@tmg/flight-form'
import ItineraryPlaceInput from '~/components/Section/FlightForm/components/ItineraryPlaceInput.vue'

const props = defineProps<{
    tripType: TripType,
    routes: FlightRoute[]
}>()

const selectedTripType = computed(() => {
    return props.tripType
})

const isVisible = ref(true)

console.log(props.routes)
</script>
