import type { $Fetch } from 'ofetch'
import { $fetch } from 'ofetch'

let httpClient: any

export function useHttp(): $Fetch {
    if (httpClient) {
        return httpClient
    }

    const config = useRuntimeConfig()

    return httpClient = $fetch.create({
        baseURL: config.public.web.apiBase,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
    })
}
