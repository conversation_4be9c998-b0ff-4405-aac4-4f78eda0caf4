import type { $Fetch } from 'ofetch'
import { $fetch } from 'ofetch'

export function useHttp(): $Fetch {
    const config = useRuntimeConfig()

    // В SSR режиме для внутренних API используем $fetch напрямую без baseURL
    // Nuxt автоматически обрабатывает внутренние запросы
    if (import.meta.server && config.public.web.apiBase.startsWith('/')) {
        return $fetch.create({
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        })
    }

    // Для клиента используем обычный baseURL
    return $fetch.create({
        baseURL: config.public.web.apiBase,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
    })
}
