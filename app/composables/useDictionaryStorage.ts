import { useStorage } from '@vueuse/core'

export function useDictionaryStorage<TValue>(storageKey: string) {
    const clientCache = useStorage<Map<string, TValue>>(storageKey, new Map(), sessionStorage, {
        writeDefaults: false,
        mergeDefaults: false,
    })

    return {
        getAll() {
            return clientCache.value
        },
        get(key: string) {
            return clientCache.value.get(key)
        },
        set(pairs: [key: string, value: TValue][]) {
            for (const [key, value] of pairs) {
                clientCache.value.set(key, value)
            }
        },
    }
}
