import { useStorage } from '@vueuse/core'

export function useDictionaryStorage<TValue>(storageKey: string) {
    // В SSR режиме sessionStorage недоступен, используем fallback
    const storage = import.meta.client ? sessionStorage : undefined

    const clientCache = useStorage<Map<string, TValue>>(storageKey, new Map(), storage, {
        writeDefaults: false,
        mergeDefaults: false,
    })

    return {
        getAll() {
            return clientCache.value
        },
        get(key: string) {
            return clientCache.value.get(key)
        },
        set(pairs: [key: string, value: TValue][]) {
            for (const [key, value] of pairs) {
                clientCache.value.set(key, value)
            }
        },
    }
}
