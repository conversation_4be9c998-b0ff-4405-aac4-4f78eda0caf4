/**
 * Composable для работы с Jivo чатом
 */
export function useJivo() {
    const { $jivo } = useNuxtApp()

    return {
        /**
         * Открыть callback кнопку Jivo программно
         */
        openCallbackButton: () => {
            $jivo.openCallbackButton()
        },

        /**
         * Отключить Jivo чат (установить cookie)
         */
        disable: () => {
            $jivo.disable()
        },

        /**
         * Включить Jivo чат (удалить cookie)
         */
        enable: () => {
            $jivo.enable()
        },

        /**
         * Проверить, отключен ли чат
         */
        isDisabled: computed(() => {
            if (import.meta.server) return true
            return $jivo.isDisabled()
        }),

        /**
         * Статус загрузки чата
         */
        isLoaded: computed(() => {
            if (import.meta.server) return false
            return !!document.querySelector('script[src*="jivosite.com"]')
        })
    }
}
