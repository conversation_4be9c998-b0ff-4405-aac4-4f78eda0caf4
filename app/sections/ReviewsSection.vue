<template>
    <div class="section pb-4">
        <div class="reviews">
            <div class="reviews__total">
                <h2 class="reviews__total__title">
                    <span class="reviews__total__title__gradient">97% of Travelers</span> Recommend <br> TravelBusinessClass.com
                </h2>
            </div>
            <div class="reviews__trustpilot">
                <img
                    class="reviews__trustpilot__image"
                    src="/images/index-sections/trustpilot.png"
                    alt="trustpilot"
                >
                <a
                    href="https://www.trustpilot.com/review/travelbusinessclass.com"
                    rel="nofollow noindex"
                    class="reviews__trustpilot__link"
                >
                    <b>Excellent, Rated 4.9 out of 5.</b> Based on 2409 reviews on Trustpilot
                </a>
            </div>
            <div class="reviews__carousel">
                <div class="swiper-button-prev">
                    <IconArrow class="text-white rotate-180" />
                </div>
                <div class="swiper-button-next">
                    <IconArrow class="text-white" />
                </div>
                <Swiper
                    v-bind="swiperOptions"
                    @breakpoint="handleBreakpoint"
                    @slide-change="handleSlideChange"
                    @autoplay-time-left="onAutoplayTimeLeft"
                >
                    <SwiperSlide
                        v-for="(review, $i) in reviews"
                        :key="$i"
                        target="_blank"
                        tag="a"
                        :href="review.link"
                        class="reviews__carousel__slide"
                    >
                        <div class="reviews__carousel__slide__item">
                            <div class="reviews__carousel__slide__item__abbreviation">
                                <div class="reviews__carousel__slide__item__abbreviation__value">
                                    {{ getAbbreviation(review.fullName) }}
                                </div>
                            </div>

                            <p class="reviews__carousel__slide__item__name">
                                {{ review.fullName }}
                            </p>

                            <img
                                src="/images/index-sections/trustpilot.png"
                                alt="trustpilot"
                                class="reviews__carousel__slide__item__trustpilot"
                                loading="lazy"
                            >

                            <p class="reviews__carousel__slide__item__comment">
                                “{{ review.text }}”
                            </p>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>
            <div ref="swiperPaginationContainerRef" class="swiper-pagination" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Navigation, Pagination } from 'swiper/modules'
import type { SwiperOptions } from 'swiper/types'

import 'swiper/css'
import 'swiper/css/scrollbar'
import { useThrottleFn } from '@vueuse/core'

const swiperOptions = {
    modules: [Autoplay, Navigation, Pagination],
    spaceBetween: 20,
    roundLengths: true,
    watchSlidesProgress: true,
    watchSlidesVisibility: true,
    speed: 400,
    loop: false,
    followFinger: true,
    cssMode: false,
    autoplay: {
        delay: 80000,
        disableOnInteraction: false,
    },
    preventInteractionOnTransition: false,
    navigation: {
        prevEl: '.swiper-button-prev',
        nextEl: '.swiper-button-next',
    },
    pagination: {
        clickable: false,
        el: '.swiper-pagination',
    },
    breakpoints: {
        640: {
            slidesPerView: 2,
            spaceBetween: 60,
            slidesPerGroup: 2,
        },
        1024: {
            slidesPerView: 2,
            spaceBetween: 20,
            slidesPerGroup: 2,
        },
        1280: {
            slidesPerView: 3,
            spaceBetween: 60,
            slidesPerGroup: 3,
        },
        1536: {
            slidesPerView: 4,
            spaceBetween: 60,
            slidesPerGroup: 4,
        },
    },
} satisfies SwiperOptions

const swiperPaginationContainerRef = ref<HTMLElement>()

const onAutoplayTimeLeft = useThrottleFn((s: unknown, time: unknown, progress: number) => {
    const percent = (1 - progress) * 100

    swiperPaginationContainerRef.value?.style.setProperty('--progress', (percent) + '%')
}, 1000 / 60)

const reviews: {
    fullName: string
    text: string
    link: string
}[]
    = [
        {
            link: 'https://www.trustpilot.com/reviews/611aa3dc9a633a1e9219b68e',
            fullName: 'Ghyam/Darya Doris',
            text: 'Emma was very helpful in finding the flights I needed and was very friendly. I will update after completing the trip.',
        },
        {
            link: 'https://www.trustpilot.com/reviews/611a6c2251602952de952fc4',
            fullName: 'Curt',
            text: 'Emma Patterson was wonderful to deal with. She was extremely helpful and responsive to our requests and was able to provide ...',
        },
        {
            link: 'https://www.trustpilot.com/reviews/6117ada59a633a1e9217de87',
            fullName: 'Ayman Salem',
            text: "Very reliable fast action. I've booked with them 7 night at one of the hotel they arranged every thing they have been i touc...",
        },
        {
            link: 'https://www.trustpilot.com/reviews/6110d27af9f487044c556cbd',
            fullName: 'Cynthia Meyer',
            text: 'I would like to take time out to thank Paul Decker people let me tell you this Man has went way and beyond to make me feel c...',
        },
        {
            link: 'https://www.trustpilot.com/reviews/610ee3ecf9f48709d4cb13d8',
            fullName: 'Mr Bellfort',
            text: 'Prompt, courteous service and follow through.',
        },
        {
            link: 'https://www.trustpilot.com/reviews/61088130f9f48709d4c63be5',
            fullName: 'Benjamin Oka',
            text: 'Emma Paterson is a good representative , she can accommodate all what we need.',
        },
        {
            link: 'https://www.trustpilot.com/reviews/61086dc3f9f487044c4fab7b',
            fullName: 'Randphuls',
            text: 'Good price and she was very helpful, and now we got seats.',
        },
        {
            link: 'https://www.trustpilot.com/reviews/61061878f9f48709d4c49954',
            fullName: 'Susan C Winter',
            text: 'Thank you for your service -so efficient, so easy, so inexpensive, and relieving.I will use your service again, for sure!!!',
        },
        {
            link: 'https://www.trustpilot.com/users/5d6efee7328569b62d95652a',
            fullName: 'Dee Setchel',
            text: 'Paul was wonderful and helped me secure a great price with a wonderful itinerary for our flight to Geneva in January.',
        },
        {
            link: 'https://www.trustpilot.com/reviews/6101730ff9f48709d4c17971',
            fullName: 'Lucia Dumitrascu',
            text: 'The agent was wonderful, very kind, and helpful. She went above and beyond to help me book a flight last minute. Great service!',
        },
        {
            link: 'https://www.trustpilot.com/reviews/61009bbff9f487044c4a50d7',
            fullName: 'Brett',
            text: 'Emma is great with customers and quick to respond and do the best to help save customers money. I highly recommend contactin...',
        },
        {
            link: 'https://www.trustpilot.com/reviews/60f94e16f9f487044c453252',
            fullName: 'MaryBeth',
            text: 'Adam the travel agent, did a very consistent and persistent job at finding me the exact flights that I needed at the right p...',
        },
    ]

const totalSlides = ref(0)
const currentBreakpoint = ref(0)

const handleSlideChange = (swiperInstance) => {
    totalSlides.value = swiperInstance.slides.length
}

const handleBreakpoint = (swiperInstance: any, breakpoint: number) => {
    currentBreakpoint.value = breakpoint
}

//

const getAbbreviation = (fullname: string) => {
    const parts = fullname.trim().split(/\s+/)

    if (parts.length > 1) {
        return `${parts[0][0]}${parts[1][0]}`.toUpperCase()
    }

    if (parts[0].length > 1) {
        return `${parts[0][0]}${parts[0][1]}`.toUpperCase()
    }

    return `${parts[0][0]}${parts[0][0]}`.toUpperCase()
}
</script>
