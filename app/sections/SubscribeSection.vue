<template>
    <div class="section">
        <div class="subscribe">
            <div class="subscribe__icon-container">
                <IconMailbox class="subscribe__icon" />
            </div>
            <div class="subscribe__form">
                <h2 class="subscribe__form__title">
                    Subscribe to our Newsletters
                </h2>
                <p class="subscribe__form__description">
                    Be the first to receive updates on our exclusive offers
                </p>
                <div class="subscribe__form__container">
                    <FormField
                        class="subscribe__form__container__name"
                        :form="form"
                        field="name"
                        label="Enter your Name"
                        hide-error-text
                    >
                        <InputText
                            v-model="form.data.name"
                            name="Name"
                            placeholder="Name"
                        />
                    </FormField>
                    <FormField
                        class="subscribe__form__container__email"
                        :form="form"
                        field="email"
                        label="Enter your e-mail"
                        hide-error-text
                    >
                        <InputText
                            v-model="form.data.email"
                            name="email"
                            placeholder="<EMAIL>"
                        />
                    </FormField>
                    <div class="subscribe__form__container__action">
                        <button class="button button--primary" @click="submit">
                            Subscribe
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useForm } from '@tmg/form'

const form = useForm({
    name: '',
    email: '',
}, {
    name: (value) => {
        if (!value || value.trim() === '') {
            return 'Name is required'
        }
    },
})

const fetch = useHttp()

const submit = form.useSubmit(async (data) => {
    await fetch('/subscribe', {
        method: 'POST',
        body: {
            name: data.name,
            email: data.email,
        },
    })
})
</script>
