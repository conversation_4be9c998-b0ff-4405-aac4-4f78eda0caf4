<template>
    <div class="section">
        <span class="section__headline">Best</span>
        <span class="section__title">Business class airlines</span>
        <div class="best-deals">
            <div class="best-deals__items-container">
                <Card
                    v-for="(deal, index) in businessAirlinesRecords"
                    :key="index"
                    :card-type="BestDealType.Airline"
                    :data="deal"
                />
            </div>

            <div class="best-deals__view-more">
                <a class="button best-deals__view-more__link">
                    <div class="relative w-full">
                        <span>View more</span>
                        <IconChevron class="best-deals__view-more__link__chevron" />
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Card from '~/components/Section/Card/Card.vue'
import { useBusinessAirlinesStore } from '~/stores/useBusinessAirlinesStore'

const businessAirlinesStore = useBusinessAirlinesStore()

const businessAirlinesRecords = computed(() => {
    return businessAirlinesStore.businessAirlines
})

const fetchData = async () => {
    await businessAirlinesStore.fetchBusinessAirlines()
}

if (!businessAirlinesRecords.value) {
    fetchData()
}
</script>
