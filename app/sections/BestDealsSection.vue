<template>
    <div class="section">
        <span class="section__headline">Fresh</span>
        <span class="section__title">Best deals</span>
        <div class="best-deals">
            <div class="best-deals__items-container">
                <Card
                    v-for="(deal, index) in bestDealRecords"
                    :key="index"
                    :card-type="BestDealType.Country"
                    :data="deal"
                />
            </div>

            <div class="best-deals__view-more">
                <a class="button best-deals__view-more__link">
                    <div class="relative w-full">
                        <span>View more</span>
                        <IconChevron class="best-deals__view-more__link__chevron" />
                    </div>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Card from '~/components/Section/Card/Card.vue'

const bestDealsStore = useBestDealsStore()

const bestDealRecords = computed(() => {
    return bestDealsStore.bestDeals
})

const fetchData = async () => {
    await bestDealsStore.fetchBestDeals()
}

if (!bestDealRecords.value) {
    fetchData()
}
</script>
