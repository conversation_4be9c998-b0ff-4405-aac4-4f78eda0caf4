import { defineStore } from 'pinia'
import type { ApiResponseType } from '~~/server/api/best-deals.get'

export enum BestDealClass {
    First = 'first',
    Business = 'business',
}

export type BestDeals = {
    route: string,
    imageSrc: string,
    averageProfit: number,
    class: BestDealClass,
    destination: string,
    cheapestPrice: number,
}

export const useBestDealsStore = defineStore('best-deals', () => {
    const fetch = useHttp()

    const bestDeals = ref<BestDeals[]>()

    const fetchBestDeals = async () => {
        const response = await fetch('/best-deals') as ApiResponseType<BestDeals[]>
        bestDeals.value = response.items
    }

    return {
        fetchBestDeals,
        bestDeals,
    }
})
