/* Jivo Chat Custom Styles */

/* Основные стили для Jivo виджета */
:global(jdiv) {
    font-family: sbc-font, sans-serif !important;
}

/* Стили для полей ввода */
:global(jdiv input) {
    border-color: #D7D7D7 !important;
}

/* Стили для кнопок */
:global(jdiv button),
:global(jdiv .jivo-button) {
    background: linear-gradient(135deg, #9d1d5a 0%, #590c32 100%) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

:global(jdiv button:hover),
:global(jdiv .jivo-button:hover) {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(157, 29, 90, 0.3) !important;
}

/* Стили для callback кнопки */
:global(jdiv.__jivoCallbackBtn) {
    background: linear-gradient(135deg, #9d1d5a 0%, #590c32 100%) !important;
    border-radius: 6px !important;
    font-family: sbc-font, sans-serif !important;
}

/* Стили для основного окна чата */
:global(jdiv.__jivoMobileWidget) {
    font-family: sbc-font, sans-serif !important;
}

/* Стили для заголовка чата */
:global(jdiv.__jivoMobileWidget .__jivoMobileWidgetHeader) {
    background: linear-gradient(135deg, #9d1d5a 0%, #590c32 100%) !important;
}

/* Стили для текста в чате */
:global(jdiv.__jivoMobileWidget .__jivoMobileWidgetBody) {
    font-family: sbc-font, sans-serif !important;
}

/* Адаптивные стили */
@media (max-width: 768px) {
    :global(jdiv.__jivoMobileWidget) {
        max-width: 90vw !important;
    }
}

/* Анимации для появления виджета */
:global(jdiv.__jivoWidget) {
    animation: jivoFadeIn 0.3s ease-in-out !important;
}

@keyframes jivoFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
