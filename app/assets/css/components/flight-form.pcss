.flight-form {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    max-width: 1152px;

    @media (min-width: 768px) {
        max-width: 768px;
    }

    @media (min-width: 1280px) {
        max-width: 1152px;
    }

    &__layout {
        position: relative;
        z-index: 10;
        padding: 4rem 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;

        @media (min-width: 640px) {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        @media (min-width: 768px) {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        @media (min-width: 1024px) {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        &__container {
            width: 100%;
            max-width: 95%;
            margin-left: auto;
            margin-right: auto;
            margin-top: 4rem;

            @media (min-width: 640px) {
                max-width: 90%;
            }

            @media (min-width: 768px) {
                max-width: 42rem;
            }

            @media (min-width: 1024px) {
                max-width: 48rem;
            }

            @media (min-width: 1280px) {
                max-width: 72rem;
            }
        }
    }
}

.trip-class {
    width: 100%;
    margin-bottom: 2.5rem;

    @media (min-width: 768px) {
        max-width: 42rem;
    }

    @media (min-width: 1024px) {
        max-width: 48rem;
    }

    @media (min-width: 1280px) {
        max-width: 72rem;
    }

    &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    &__subheading {
        text-transform: uppercase;
        color: white;
        font-size: 0.875rem;
        font-weight: 700;
        letter-spacing: 0.1em;
    }

    &__body {
        display: none;

        @media (min-width: 1024px) {
            display: flex;
            align-items: center;
            margin-top: 1rem;
        }
    }

    &__title {
        color: white;
        font-size: 2.25rem;
        font-weight: 700;
    }

    &__icon-svg {
        width: 1.5rem;
        height: 1.5rem;
        stroke: currentColor;
    }

    &__option-svg {
        width: 1rem;
        height: 1rem;
        stroke: currentColor;
    }

    &__selector {
        position: relative;
        height: 3.25rem;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 9999px;
        z-index: 40;
        margin-left: 0.75rem;
        margin-right: 0.75rem;

        &__wrapper {
            position: relative;
        }

        &__backdrop {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 75ms;
            width: 100%;
            height: 100%;
            inset: 0;
            opacity: 1;
        }

        &__background {
            visibility: hidden;
            position: absolute;
            opacity: 0;
            inset: 0;
            height: 100%;
            padding-top: 0.875rem;
            box-sizing: content-box;
            border-radius: 1.5rem;
            transform: scaleY(0.75);
            transform-origin: top;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: transform 150ms cubic-bezier(0.34, 1.56, 0.64, 1),
            opacity 150ms cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;
        }

        &__caption {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            line-height: 1;
            padding-left: 1.75rem;
            color: #ffffff;
            transition: color 75ms ease-in;
            will-change: color;
        }

        &__label {
            font-size: 2.25rem;
            font-weight: 700;
            margin-top: -0.25rem;
        }

        &__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3.25rem;
            height: 3.25rem;
            border-radius: 9999px;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 1.5rem;
        }

        &__list {
            overflow: hidden;
            position: relative;
            border-bottom-left-radius: 2.5rem;
            border-bottom-right-radius: 2.5rem;
            max-height: 0;
        }

        &__options {
            padding-bottom: 0.625rem;
        }

        &__option {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 0.875rem;
            margin: 0 10px;
            padding: 0.625rem 1.75rem;
            opacity: 0;
            cursor: pointer;
            transform: translateY(0.75rem);
            transition: transform 350ms cubic-bezier(0.34, 1.56, 0.64, 1),
            opacity 350ms cubic-bezier(0.34, 1.56, 0.64, 1);
            will-change: transform, opacity;

            &-icon {
                transform: translateX(-100%);
                opacity: 0;
                transition: transform 300ms, opacity 300ms;
                will-change: transform, opacity;
                margin-right: 0.25rem;
            }

            &-label {
                transform: translateX(-0.875rem);
                transition: transform 300ms;
                will-change: transform;
                color: #4A94EC;
            }

            &:hover {
                background-color: #EDF5FE;
                transition: none;
                border-radius: 10px;

                .trip-class__selector__option-label {
                    transform: translateX(0);
                    color: #282F36;
                }

                .trip-class__selector__option-icon {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        }

        &--open {
            .trip-class__selector__background {
                visibility: visible;
                background-color: #ffffff;
                opacity: 1;
                transform: none;
            }

            .trip-class__selector__caption {
                color: #374151;
            }

            .trip-class__selector__list {
                max-height: 24rem;
            }

            .trip-class__selector__option {
                opacity: 1;
                transform: translateY(0);

                &:nth-child(1) { transition-delay: 50ms; }
                &:nth-child(2) { transition-delay: 100ms; }
                &:nth-child(3) { transition-delay: 150ms; }
                &:nth-child(4) { transition-delay: 200ms; }
                &:nth-child(5) { transition-delay: 250ms; }
            }
        }
    }
}

.trip-type-selector {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    justify-content: center;
    gap: 25px;

    &__button {
        font-weight: 700;
        font-size: 14px;
        color: #f4f6f7;
        opacity: 0.5;
        transition: all 0.3s ease;
        background: none;
        border: none;
        cursor: pointer;

        &--active {
            opacity: 1;
        }
    }
}

.itinerary-manager {
    background-color: transparent;
    position: relative;
    z-index: 20;
    width: 100%;

    &__container {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        width: 100%;
    }

    &__item {
        display: flex;
        gap: 0.625rem;
        width: 100%;
        position: relative;
        border-radius: 99999px;
        padding: 0.25rem;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        background-color: transparent;

        @media (min-width: 1280px) {
            flex-wrap: nowrap;
            background-color: var(--theme-color-base-white);
        }

        &__cities {
            width: 100%;
            display: grid;
            background: var(--theme-color-base-white);
            border-radius: 30px;
            position: relative;

            @media (min-width: 1280px)  {
                max-width: unset;
                grid-template-columns: repeat(2, 1fr);
                flex: 2;
            }

            &__switch-button {
                cursor: pointer;
                background: linear-gradient(242.97deg, #590C32 0%, #9D1D5A 100%);
                color: var(--theme-color-base-white);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 2.25rem;
                width: 2.25rem;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: calc(50% + 10px);

                @media (max-width: 1280px) {
                    position: absolute;
                    top: 50%;
                    right: 10px;
                    transform: translateY(-50%);
                    z-index: 11;
                    visibility: hidden;

                    &--visible {
                        visibility: unset;

                    }

                }

                svg {
                    @media (min-width: 1280px) {
                        transform: rotate(90deg);
                    }
                }
            }
        }
    }
}

.itinerary-place {
    position: relative;
    width: 100%;
    height: 55px;

    &:first-child::before {
        content: '';
        display: block;
        position: absolute;
        bottom: 0;
        height: 1px;
        width: 100%;
        background-color: #eee;
        z-index: 11;
    }

    &--open {
        &:first-child::before {
            background-color: #b4b4b4;
        }
    }

    &--open-options {
        &::before {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            height: 1px;
            width: 100%;
            background-color: #b4b4b4;
            z-index: 11;
        }
    }

    @media (min-width: 1280px) {
        &:first-child::before,
        &::before {
            display: none;
        }
    }

    &__wrapper {
        width: 100%;
        overflow: hidden;
        border-radius: 30px;
        transition: 0.2s padding cubic-bezier(0.65, 0.05, 0.36, 1), 0.2s margin cubic-bezier(0.65, 0.05, 0.36, 1);

        &--open {
            height: auto;
            background: #fff;
            position: absolute;
            z-index: 15;

            @media (min-width: 1280px) {
                margin: -1.5rem;
                padding: 1.5rem;
                width: 30rem;
                box-shadow: 0 14px 14px rgb(0 0 0 / 25%);
            }
        }
    }

    &__field {
        cursor: pointer;
        align-items: center;
        padding: 0.5rem;
        margin: 0;
        transition: 0.2s all cubic-bezier(0.65, 0.05, 0.36, 1);

        &--open {
            label {
                color: red;
                background: linear-gradient(242.97deg, #590c32 0%, #9d1d5a 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;

                &::before {
                    display: none;
                }
            }
        }

        @media (min-width: 1280px) {
            &::before {
                content: '';
                position: absolute;
                display: block;
                width: 1px;
                height: calc(100% - 1.25rem);
                top: 50%;
                right: 0;
                background-color: #e4e4e4;
                transform: translateY(-50%);
            }
        }

        &__wrapper {
            display: grid;
            grid-template-columns: 1.75rem 1fr;
            grid-gap: 0.5rem;
            position: relative;
            align-items: center;

            @media (min-width: 768px) {
                grid-template-columns: 2.25rem 1fr;
            }

            @media (min-width: 1280px) {
                &--open {
                    padding-bottom: 1.5rem;

                    &::before {
                        position: absolute;
                        content: '';
                        display: block;
                        width: 31rem;
                        height: 1px;
                        bottom: 0;
                        left: 0;
                        margin: 0 -2.5rem;
                        background-color: #b4b4b4;
                    }
                }
            }
        }

        &__icon {
            height: 1.75rem;
            width: 1.75rem;

            display: flex;
            justify-content: center;
            align-items: center;

            @media (min-width: 768px){
                height: 2.25rem;
                width: 2.25rem;
            }
        }

        &__options {
            width: calc(100% + 20px);
            margin: 1rem -10px 0;

            max-height: 18rem;
            overflow-y: scroll;
            -ms-scroll-chaining: none;
            overscroll-behavior: contain;
            -ms-overflow-style: none;
            scrollbar-width: none;
            &::-webkit-scrollbar {
                display: none;
            }

            &__item {
                border-radius: 9999px;
                cursor: pointer;
                display: flex;
                padding: 0.625rem;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;

                &:hover {
                    --tw-gradient-from: #590c32;
                    --tw-gradient-to: #9d1d5a;
                    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
                    --tw-gradient-stops: var(--tw-gradient-from),
                    var(--tw-gradient-to, rgba(89, 12, 50, 0));

                    .itinerary-place__field__options__item__info__city, .itinerary-place__field__options__item__info__country, .itinerary-place__field__options__item__code {
                        --tw-text-opacity: 1;
                        color: rgba(244, 246, 247, var(--tw-text-opacity));
                    }
                }

                &__icon {
                    border-radius: 9999px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    overflow: hidden;
                    width: 100%;

                    &__container {
                        border-radius: 9999px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 36px;
                        width: 36px;
                        overflow: hidden;
                    }

                    &--airport {
                        --tw-bg-opacity: 1;
                        background-color: rgba(241, 241, 241, var(--tw-bg-opacity));
                        border-radius: 9999px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 36px;
                        width: 36px;
                    }
                }

                &__info {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;
                    line-height: 1;
                    margin-left: 0.625rem;

                    &__city {
                        font-weight: 700;
                        font-size: 14px;
                        line-height: 1.25rem;
                    }

                    &__country {
                        font-size: 12px;
                        line-height: 1rem;
                        --tw-text-opacity: 1;
                        color: rgba(113, 119, 125, var(--tw-text-opacity));
                    }
                }

                &__code {
                    -webkit-background-clip: text;
                    background-clip: text;
                    background-image: linear-gradient(270deg, var(--tw-gradient-stops));
                    --tw-gradient-from: #590c32;
                    --tw-gradient-stops: var(--tw-gradient-from),
                    var(--tw-gradient-to, rgba(89, 12, 50, 0));
                    --tw-gradient-to: #9d1d5a;
                    font-weight: 700;
                    font-size: 14px;
                    line-height: 20px;
                    color: transparent;
                    text-transform: uppercase;
                }
            }
        }
    }

    &__input {
        background-color: transparent;
        cursor: pointer;
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        width: 100%;

        &:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
        }

        &__container {
            display: grid;
            grid-template-columns: 1fr;
        }

        &__label {
            cursor: pointer;
            font-weight: 700;
            font-size: 0.625rem;
            --tw-text-opacity: 1;
            color: rgba(180, 180, 180, 1);
            text-transform: uppercase;
        }
    }
}
