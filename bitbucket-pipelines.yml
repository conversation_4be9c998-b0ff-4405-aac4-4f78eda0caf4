# Workflow Configuration

image: node:22

definitions:
    caches:
        nodecustom:
            key:
                files:
                    - yarn.lock
            path: ./node_modules

    steps:
        -   step: &build_dev
                name: Development Build
                caches:
                    - nodecustom
                script:
                    - echo $BITBUCKET_BRANCH
                    - if [ -z ${BITBUCKET_BRANCH+x} ]; then exit 1; fi
                    - yarn install
                    - NUXT_APP_CDN_URL=$AWS_BUCKET_PATH_URL/$BITBUCKET_BRANCH/public yarn build:dev
                artifacts:
                    - .output/**


        -   step: &build_prod
                name: Production Build
                caches:
                    - nodecustom
                script:
                    - yarn install
                    - NUXT_APP_CDN_URL=$AWS_BUCKET_PATH_URL/$BITBUCKET_BUILD_NUMBER/public yarn build:prod
                artifacts:
                    - .output/**

        -   step: &deploy_branch
                name: Deploy Build
                caches:
                    - docker
                clone:
                    enabled: false
                script:
                    -   pipe: atlassian/aws-s3-deploy:1.6.1
                        variables:
                            AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                            AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                            AWS_DEFAULT_REGION: 'fra1'
                            S3_BUCKET: "tmg/${AWS_BUCKET_PATH}/${BITBUCKET_BRANCH}"
                            LOCAL_PATH: '.output'
                            EXTRA_ARGS: '--endpoint=https://fra1.digitaloceanspaces.com/ --acl=public-read'
                            CACHE_CONTROL: 'max-age=60'

        -   step: &deploy_prod
                name: Deploy
                caches:
                    - docker
                clone:
                    enabled: false
                script:
                    -   pipe: atlassian/aws-s3-deploy:1.6.1
                        variables:
                            AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                            AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                            AWS_DEFAULT_REGION: 'fra1'
                            S3_BUCKET: "tmg/${AWS_BUCKET_PATH}/${BITBUCKET_BUILD_NUMBER}"
                            LOCAL_PATH: '.output'
                            EXTRA_ARGS: '--endpoint=https://fra1.digitaloceanspaces.com/ --acl=public-read'

        -   step: &add_prod_tag
                name: Tag version
                script:
                    - git tag PROD-${BITBUCKET_BUILD_NUMBER} ${BITBUCKET_COMMIT}
                    - git push origin --tags


pipelines:
    custom:
        create_dev_build:
            -   step: *build_dev
            -   step: *deploy_branch

        create_prod_build:
            -   step: *build_prod
            -   step: *deploy_prod
            -   step: *add_prod_tag
