// https://nuxt.com/docs/api/configuration/nuxt-config
import { basename } from 'path'
import path from 'node:path'

//

const cdnUrl = process.env.NUXT_APP_CDN_URL || ''

function resolveCdnUrl(url: string) {
    return cdnUrl + url
}

//

const outputDir = path.resolve('./.output-full')

// noinspection RedundantConditionalExpressionJS,HttpUrlsUsage
export default defineNuxtConfig({
    compatibilityDate: '2025-05-23',

    ssr: true,

    devtools: {
        enabled: false,
    },

    future: {
        compatibilityVersion: 4,
    },

    modules: [
        process.env.NODE_ENV === 'test' ? '@nuxt/test-utils/module' : undefined,
        '@nuxtjs/tailwindcss',
        'nuxt-svgo',
        '@pinia/nuxt',
        '@nuxt/scripts',
        '@nuxt/image',
        './modules/test-pages',
    ].filter(Boolean),

    /**
     * Mock environment variables go here, production variables should be set in the .env file
     *
     * @see https://nuxt.com/docs/guide/going-further/runtime-config#example for more information about .env naming
     */
    runtimeConfig: {
        public: {
            debug: true,

            aeroData: {
                apiBase: 'https://aero.stage.tmgclick.com',
            },

            web: {
                apiBase: '/api',
            },

            enableTestRoutes: false,

            scripts: {
                googleTagManager: {
                    id: '',
                },
            },

            jivo: {
                widgetId: 'dwzeNfFJka',
                enabled: true,
                loadDelay: 5000,
            },
        },
    },

    appConfig: {
        layout: {
            breakpoints: {
                mobile: 450,
                tablet: 768,
                laptop: 1140,
                desktop: 1440,
            },
        },

        company: {
            name: 'TravelBusinessClass',
            abbreviation: 'TBC',
            phone: '',
        },
    },

    scripts: {
        registry: Boolean(process.env.NUXT_PUBLIC_SCRIPTS_GOOGLE_TAG_MANAGER_ID) ? {
            googleTagManager: true,
        } : {},
    },

    css: [
        '~/assets/css/main.pcss',
        '~/assets/css/jivo.pcss',
    ],

    svgo: {
        componentPrefix: 'icon-',
        autoImportPath: '~/assets/icons',
        defaultImport: 'component',
        svgoConfig: {
            multipass: true,
            plugins: [
                {
                    name: 'preset-default',
                    params: {
                        overrides: {
                            inlineStyles: {
                                onlyMatchedOnce: false,
                            },
                            removeViewBox: false,
                        },
                    },
                },
                // @ts-ignore
                {
                    name: 'prefixIds',
                    params: {
                        prefix: (node: any, { path }: {
                            path: string
                        }) => {
                            return basename(path.split('.').shift() ?? '')
                        },
                    },
                },
            ],
        },
    },

    tailwindcss: {
        cssPath: false,
    },

    vite: {
        build: {
            minify: process.env.NUXT_VITE_BUILD_MINIFY === 'false' ? false : true,
        },
    },

    nitro: {
        output: {
            dir: outputDir,
        },
    },

    app: {
        head: {
            htmlAttrs: {
                lang: 'en-US',
                prefix: 'og: http://ogp.me/ns# fb: http://ogp.me/ns/fb#',
            },

            meta: [
                {
                    name: 'viewport',
                    content: 'width=device-width, initial-scale=1, minimum-scale=0.5, maximum-scale=3,' +
                        ' user-scalable=yes',
                },
                {
                    name: 'theme-color',
                    content: '#8a194f',
                },
                {
                    name: 'msapplication-TileColor',
                    content: '#8a194f',
                },
            ],
            link: [
                {
                    rel: 'shortcut icon',
                    type: 'image/png',
                    href: resolveCdnUrl('/favicon-96x96.png'),
                },
                {
                    rel: 'shortcut icon',
                    href: resolveCdnUrl('/favicon.ico'),
                },
                {
                    rel: 'apple-touch-icon',
                    sizes: '180x180',
                    href: resolveCdnUrl('/apple-touch-icon.png'),
                },
                {
                    rel: 'icon',
                    type: 'image/svg+xml',
                    href: resolveCdnUrl('/favicon.svg'),
                },
                {
                    rel: 'manifest',
                    href: resolveCdnUrl('/site.webmanifest'),
                },
            ],
        },
    },
})
